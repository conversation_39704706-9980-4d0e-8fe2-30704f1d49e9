/**
 * Date utility functions for consistent timezone handling
 * All functions use Asia/Makassar timezone (UTC+8)
 */

/**
 * Format date for input fields without timezone conversion issues
 * @param {string} dateString - Date string from server
 * @returns {string} - Date in YYYY-MM-DD format
 */
export function formatDateForInput(dateString) {
    if (!dateString) return '';
    
    // If it's already in YYYY-MM-DD format, return as is
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        return dateString;
    }
    
    // Handle different date formats from server
    let cleanDateString = dateString;
    
    if (dateString.includes('T')) {
        // ISO format: YYYY-MM-DDTHH:MM:SS.000000Z
        cleanDateString = dateString.split('T')[0];
    } else if (dateString.includes(' ')) {
        // MySQL format: YYYY-MM-DD HH:MM:SS
        cleanDateString = dateString.split(' ')[0];
    }
    
    return cleanDateString;
}

/**
 * Get today's date in YYYY-MM-DD format (Asia/Makassar timezone)
 * @returns {string} - Today's date in YYYY-MM-DD format
 */
export function getTodayFormatted() {
    const today = new Date();
    // Use local date components to avoid timezone conversion
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * Get date N days from today in YYYY-MM-DD format
 * @param {number} days - Number of days to add (negative for past dates)
 * @returns {string} - Date in YYYY-MM-DD format
 */
export function getDateFromToday(days) {
    const date = new Date();
    date.setDate(date.getDate() + days);
    // Use local date components to avoid timezone conversion
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * Format date for display in Indonesian format (1 Januari 2025)
 * @param {string|Date} dateInput - Date string or Date object
 * @returns {string} - Formatted date in Indonesian
 */
export function formatDateIndonesian(dateInput) {
    if (!dateInput) return '-';
    
    const monthNames = [
        'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];
    
    let date;
    if (typeof dateInput === 'string') {
        // Handle string input - avoid timezone conversion
        if (dateInput.includes('T') || dateInput.includes(' ')) {
            // Extract date part only
            const datePart = dateInput.split('T')[0].split(' ')[0];
            const [year, month, day] = datePart.split('-').map(Number);
            date = new Date(year, month - 1, day); // month is 0-indexed
        } else {
            const [year, month, day] = dateInput.split('-').map(Number);
            date = new Date(year, month - 1, day);
        }
    } else {
        date = dateInput;
    }
    
    if (isNaN(date.getTime())) return '-';
    
    const day = date.getDate();
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();
    
    return `${day} ${month} ${year}`;
}

/**
 * Format date for display in DD/MM/YYYY format
 * @param {string|Date} dateInput - Date string or Date object
 * @returns {string} - Formatted date in DD/MM/YYYY
 */
export function formatDateDDMMYYYY(dateInput) {
    if (!dateInput) return '-';
    
    let date;
    if (typeof dateInput === 'string') {
        // Handle string input - avoid timezone conversion
        if (dateInput.includes('T') || dateInput.includes(' ')) {
            // Extract date part only
            const datePart = dateInput.split('T')[0].split(' ')[0];
            const [year, month, day] = datePart.split('-').map(Number);
            date = new Date(year, month - 1, day); // month is 0-indexed
        } else {
            const [year, month, day] = dateInput.split('-').map(Number);
            date = new Date(year, month - 1, day);
        }
    } else {
        date = dateInput;
    }
    
    if (isNaN(date.getTime())) return dateInput;
    
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
}

/**
 * Add days to a date string and return in YYYY-MM-DD format
 * @param {string} dateString - Date in YYYY-MM-DD format
 * @param {number} days - Number of days to add
 * @returns {string} - New date in YYYY-MM-DD format
 */
export function addDaysToDate(dateString, days) {
    if (!dateString) return '';
    
    const [year, month, day] = dateString.split('-').map(Number);
    const date = new Date(year, month - 1, day); // month is 0-indexed
    date.setDate(date.getDate() + days);
    
    const newYear = date.getFullYear();
    const newMonth = String(date.getMonth() + 1).padStart(2, '0');
    const newDay = String(date.getDate()).padStart(2, '0');
    
    return `${newYear}-${newMonth}-${newDay}`;
}

/**
 * Get first day of current month in YYYY-MM-DD format
 * @returns {string} - First day of current month
 */
export function getFirstDayOfMonth() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}-01`;
}

/**
 * Get last day of current month in YYYY-MM-DD format
 * @returns {string} - Last day of current month
 */
export function getLastDayOfMonth() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    const lastDay = new Date(year, month + 1, 0).getDate();
    const monthStr = String(month + 1).padStart(2, '0');
    const dayStr = String(lastDay).padStart(2, '0');
    return `${year}-${monthStr}-${dayStr}`;
}

// Make functions available globally for backward compatibility
if (typeof window !== 'undefined') {
    window.DateUtils = {
        formatDateForInput,
        getTodayFormatted,
        getDateFromToday,
        formatDateIndonesian,
        formatDateDDMMYYYY,
        addDaysToDate,
        getFirstDayOfMonth,
        getLastDayOfMonth
    };
}
